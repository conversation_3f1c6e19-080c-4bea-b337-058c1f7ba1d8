#!/usr/bin/env python3
"""
Simple script to verify path simulation accuracy by comparing toAmount values
between path allocation results and benchmark data.
"""

import json
import os
import glob
import pandas as pd
from decimal import Decimal
import argparse
import re


def load_json_file(file_path: str) -> dict:
    """Load JSON file and return data."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None


def extract_info_from_filename(filename: str) -> tuple:
    """Extract block number, token pair, and amount from filename."""
    # Pattern: blockNumber_token1-token2_amount.json
    pattern = r'(\d+)_([^_]+)-([^_]+)_(\d+)\.json'
    match = re.match(pattern, filename)
    
    if match:
        block_number = match.group(1)
        token1 = match.group(2).upper()
        token2 = match.group(3).upper()
        amount = match.group(4)
        return block_number, token1, token2, amount
    
    return None, None, None, None


def find_matching_benchmark(path_allocation_file: str, benchmark_dir: str) -> str:
    """Find the matching benchmark file for a path allocation file."""
    filename = os.path.basename(path_allocation_file)
    
    # Extract info from path allocation filename
    block_number, token1, token2, amount = extract_info_from_filename(filename)
    
    if not all([block_number, token1, token2, amount]):
        return None
    
    # Look for benchmark file with same pattern
    benchmark_pattern = f"{block_number}_{token1.lower()}-{token2.lower()}_{amount}.json"
    benchmark_file = os.path.join(benchmark_dir, benchmark_pattern)
    
    if os.path.exists(benchmark_file):
        return benchmark_file
    
    return None


def compare_to_amounts(path_allocation_dir: str, benchmark_dir: str) -> list:
    """Compare toAmount values between path allocation and benchmark files."""
    
    results = []
    path_files = glob.glob(os.path.join(path_allocation_dir, "*.json"))
    
    print(f"Found {len(path_files)} path allocation files\n")
    
    for path_file in path_files:
        filename = os.path.basename(path_file)
        
        # Load path allocation data
        path_data = load_json_file(path_file)
        if not path_data:
            continue
        
        # Find matching benchmark
        benchmark_file = find_matching_benchmark(path_file, benchmark_dir)
        if not benchmark_file:
            print(f"❌ {filename}: No matching benchmark found")
            continue
        
        # Load benchmark data
        benchmark_data = load_json_file(benchmark_file)
        if not benchmark_data:
            continue
        
        # Extract toAmount values
        path_to_amount = path_data.get('toAmount')
        benchmark_to_amount = benchmark_data.get('toAmount')
        
        if not path_to_amount or not benchmark_to_amount:
            print(f"❌ {filename}: Missing toAmount in one of the files")
            continue
        
        # Convert to Decimal for precise calculation
        try:
            path_decimal = Decimal(str(path_to_amount))
            benchmark_decimal = Decimal(str(benchmark_to_amount))
            
            # Calculate difference
            diff = path_decimal - benchmark_decimal
            
            # Calculate percentage difference
            if benchmark_decimal != 0:
                pct_diff = (diff / benchmark_decimal) * 100
            else:
                pct_diff = None
            
            # Check if path allocation result is valid (non-negative)
            is_valid = path_decimal >= 0
            
            # Determine status
            if not is_valid:
                status = "❌ ERROR"
                reason = "Negative toAmount"
            elif abs(pct_diff) < 0.001:  # Less than 0.001% difference
                status = "✅ MATCH"
                reason = "Very close match"
            elif pct_diff > 0:
                status = "📈 BETTER"
                reason = f"+{pct_diff:.4f}%"
            else:
                status = "📉 WORSE"
                reason = f"{pct_diff:.4f}%"
            
            result = {
                'filename': filename,
                'status': status,
                'reason': reason,
                'path_to_amount': float(path_decimal),
                'benchmark_to_amount': float(benchmark_decimal),
                'difference': float(diff),
                'percentage_diff': float(pct_diff) if pct_diff is not None else None,
                'is_valid': is_valid
            }
            
            results.append(result)
            
            # Print result
            print(f"{status} {filename}")
            print(f"    Path:      {path_decimal:,}")
            print(f"    Benchmark: {benchmark_decimal:,}")
            print(f"    Reason:    {reason}")
            print()
            
        except Exception as e:
            print(f"❌ {filename}: Error calculating difference - {e}")
            continue
    
    return results


def print_summary(results: list):
    """Print summary statistics."""
    if not results:
        print("No results to summarize")
        return
    
    total = len(results)
    valid_results = [r for r in results if r['is_valid']]
    error_results = [r for r in results if not r['is_valid']]
    
    print("="*60)
    print("SIMULATION ACCURACY SUMMARY")
    print("="*60)
    print(f"Total comparisons: {total}")
    print(f"Valid results: {len(valid_results)} ({len(valid_results)/total*100:.1f}%)")
    print(f"Error cases: {len(error_results)} ({len(error_results)/total*100:.1f}%)")
    
    if valid_results:
        # Count by status
        matches = len([r for r in valid_results if "MATCH" in r['status']])
        better = len([r for r in valid_results if "BETTER" in r['status']])
        worse = len([r for r in valid_results if "WORSE" in r['status']])
        
        print(f"\nValid Results Breakdown:")
        print(f"  Exact/Close matches: {matches} ({matches/len(valid_results)*100:.1f}%)")
        print(f"  Path allocation better: {better} ({better/len(valid_results)*100:.1f}%)")
        print(f"  Benchmark better: {worse} ({worse/len(valid_results)*100:.1f}%)")
        
        # Statistics on percentage differences
        pct_diffs = [r['percentage_diff'] for r in valid_results if r['percentage_diff'] is not None]
        if pct_diffs:
            print(f"\nPercentage Difference Statistics:")
            print(f"  Mean: {sum(pct_diffs)/len(pct_diffs):.6f}%")
            print(f"  Min: {min(pct_diffs):.6f}%")
            print(f"  Max: {max(pct_diffs):.6f}%")
    
    if error_results:
        print(f"\nError Cases:")
        for result in error_results:
            print(f"  {result['filename']}: {result['reason']}")
    
    # Overall assessment
    print(f"\n{'='*60}")
    if len(error_results) == 0:
        if len(valid_results) > 0:
            avg_diff = sum([abs(r['percentage_diff']) for r in valid_results if r['percentage_diff'] is not None]) / len(valid_results)
            if avg_diff < 0.01:
                print("🎉 SIMULATION ACCURACY: EXCELLENT (Average difference < 0.01%)")
            elif avg_diff < 0.1:
                print("✅ SIMULATION ACCURACY: GOOD (Average difference < 0.1%)")
            elif avg_diff < 1.0:
                print("⚠️  SIMULATION ACCURACY: FAIR (Average difference < 1.0%)")
            else:
                print("❌ SIMULATION ACCURACY: POOR (Average difference >= 1.0%)")
        else:
            print("❓ SIMULATION ACCURACY: UNKNOWN (No valid comparisons)")
    else:
        print("❌ SIMULATION ACCURACY: ISSUES DETECTED (Some cases have errors)")


def main():
    parser = argparse.ArgumentParser(description='Verify path simulation accuracy')
    parser.add_argument('--path-allocation-dir', 
                       default='reranking/results/path_allocation_json',
                       help='Directory containing path allocation JSON files')
    parser.add_argument('--benchmark-dir', 
                       default='data/benchmark',
                       help='Directory containing benchmark JSON files')
    parser.add_argument('--output', 
                       help='Optional CSV file to save detailed results')
    
    args = parser.parse_args()
    
    # Check if directories exist
    if not os.path.exists(args.path_allocation_dir):
        print(f"Error: Path allocation directory not found: {args.path_allocation_dir}")
        return
    
    if not os.path.exists(args.benchmark_dir):
        print(f"Error: Benchmark directory not found: {args.benchmark_dir}")
        return
    
    print("Path Simulation Accuracy Verification")
    print("="*50)
    print(f"Path allocation directory: {args.path_allocation_dir}")
    print(f"Benchmark directory: {args.benchmark_dir}")
    print()
    
    # Run comparison
    results = compare_to_amounts(args.path_allocation_dir, args.benchmark_dir)
    
    # Print summary
    print_summary(results)
    
    # Save to CSV if requested
    if args.output and results:
        df = pd.DataFrame(results)
        df.to_csv(args.output, index=False)
        print(f"\nDetailed results saved to: {args.output}")


if __name__ == "__main__":
    main()
