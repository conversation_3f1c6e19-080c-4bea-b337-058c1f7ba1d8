#!/usr/bin/env python3
"""
Script to compare toAmount values between path allocation results and benchmark data.
This script analyzes the performance difference between the path allocation algorithm
and the benchmark routing results.
"""

import json
import os
import glob
import pandas as pd
from decimal import Decimal
from pathlib import Path
import argparse
import re


def load_json_file(file_path: str) -> dict:
    """Load JSON file and return data."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None


def extract_info_from_filename(filename: str) -> tuple:
    """Extract block number, token pair, and amount from filename."""
    # Pattern: blockNumber_token1-token2_amount.json
    pattern = r'(\d+)_([^_]+)-([^_]+)_(\d+)\.json'
    match = re.match(pattern, filename)
    
    if match:
        block_number = match.group(1)
        token1 = match.group(2).upper()
        token2 = match.group(3).upper()
        amount = match.group(4)
        return block_number, token1, token2, amount
    
    return None, None, None, None


def find_matching_benchmark(path_allocation_file: str, benchmark_dir: str) -> str:
    """Find the matching benchmark file for a path allocation file."""
    filename = os.path.basename(path_allocation_file)
    
    # Extract info from path allocation filename
    block_number, token1, token2, amount = extract_info_from_filename(filename)
    
    if not all([block_number, token1, token2, amount]):
        return None
    
    # Look for benchmark file with same pattern
    benchmark_pattern = f"{block_number}_{token1.lower()}-{token2.lower()}_{amount}.json"
    benchmark_file = os.path.join(benchmark_dir, benchmark_pattern)
    
    if os.path.exists(benchmark_file):
        return benchmark_file
    
    # Try alternative pattern (some files might have different casing)
    benchmark_pattern_alt = f"{block_number}_{token2.lower()}-{token1.lower()}_{amount}.json"
    benchmark_file_alt = os.path.join(benchmark_dir, benchmark_pattern_alt)
    
    if os.path.exists(benchmark_file_alt):
        return benchmark_file_alt
    
    return None


def calculate_difference_metrics(path_amount: str, benchmark_amount: str) -> dict:
    """Calculate various difference metrics between path allocation and benchmark."""
    try:
        path_decimal = Decimal(path_amount)
        benchmark_decimal = Decimal(benchmark_amount)

        # Check for invalid values
        is_path_valid = path_decimal >= 0
        is_benchmark_valid = benchmark_decimal >= 0

        # Absolute difference
        abs_diff = path_decimal - benchmark_decimal

        # Percentage difference (relative to benchmark)
        if benchmark_decimal != 0:
            pct_diff = (abs_diff / benchmark_decimal) * 100
        else:
            pct_diff = None

        # Efficiency ratio
        path_efficiency = float(path_decimal)
        benchmark_efficiency = float(benchmark_decimal)

        return {
            'path_amount': float(path_decimal),
            'benchmark_amount': float(benchmark_decimal),
            'absolute_difference': float(abs_diff),
            'percentage_difference': float(pct_diff) if pct_diff is not None else None,
            'path_efficiency': path_efficiency,
            'benchmark_efficiency': benchmark_efficiency,
            'is_better': abs_diff > 0 and is_path_valid,
            'is_path_valid': is_path_valid,
            'is_benchmark_valid': is_benchmark_valid,
            'has_error': not (is_path_valid and is_benchmark_valid)
        }
    except Exception as e:
        print(f"Error calculating metrics: {e}")
        return None


def compare_allocations(path_allocation_dir: str, benchmark_dir: str, output_file: str = None) -> pd.DataFrame:
    """Compare all path allocation results with their corresponding benchmarks."""
    
    results = []
    
    # Get all path allocation JSON files
    path_files = glob.glob(os.path.join(path_allocation_dir, "*.json"))
    
    print(f"Found {len(path_files)} path allocation files")
    
    for path_file in path_files:
        filename = os.path.basename(path_file)
        print(f"\nProcessing: {filename}")
        
        # Load path allocation data
        path_data = load_json_file(path_file)
        if not path_data:
            continue
        
        # Find matching benchmark
        benchmark_file = find_matching_benchmark(path_file, benchmark_dir)
        if not benchmark_file:
            print(f"  No matching benchmark found")
            continue
        
        print(f"  Found benchmark: {os.path.basename(benchmark_file)}")
        
        # Load benchmark data
        benchmark_data = load_json_file(benchmark_file)
        if not benchmark_data:
            continue
        
        # Extract toAmount values
        path_to_amount = path_data.get('toAmount')
        benchmark_to_amount = benchmark_data.get('toAmount')
        
        if not path_to_amount or not benchmark_to_amount:
            print(f"  Missing toAmount in one of the files")
            continue
        
        # Calculate metrics
        metrics = calculate_difference_metrics(path_to_amount, benchmark_to_amount)
        if not metrics:
            continue
        
        # Extract additional info
        block_number, token1, token2, amount = extract_info_from_filename(filename)
        
        result = {
            'filename': filename,
            'block_number': block_number,
            'token_pair': f"{token1}-{token2}",
            'from_token': token1,
            'to_token': token2,
            'input_amount': amount,
            'path_to_amount': metrics['path_amount'],
            'benchmark_to_amount': metrics['benchmark_amount'],
            'absolute_difference': metrics['absolute_difference'],
            'percentage_difference': metrics['percentage_difference'],
            'is_path_better': metrics['is_better'],
            'is_path_valid': metrics['is_path_valid'],
            'is_benchmark_valid': metrics['is_benchmark_valid'],
            'has_error': metrics['has_error'],
            'benchmark_file': os.path.basename(benchmark_file)
        }
        
        results.append(result)
        
        # Print summary for this comparison
        print(f"  Path allocation toAmount: {metrics['path_amount']:,.0f}")
        print(f"  Benchmark toAmount:       {metrics['benchmark_amount']:,.0f}")
        print(f"  Difference:               {metrics['absolute_difference']:,.0f}")
        if metrics['percentage_difference'] is not None:
            print(f"  Percentage difference:    {metrics['percentage_difference']:.4f}%")
        print(f"  Path allocation better:   {metrics['is_better']}")
        if metrics['has_error']:
            print(f"  ⚠️  WARNING: Invalid values detected!")
            if not metrics['is_path_valid']:
                print(f"     Path allocation has negative toAmount")
            if not metrics['is_benchmark_valid']:
                print(f"     Benchmark has negative toAmount")
    
    # Create DataFrame
    df = pd.DataFrame(results)
    
    if len(df) > 0:
        # Sort by percentage difference (descending)
        df = df.sort_values('percentage_difference', ascending=False)
        
        # Save to file if specified
        if output_file:
            df.to_csv(output_file, index=False)
            print(f"\nResults saved to: {output_file}")
        
        # Print summary statistics
        print(f"\n{'='*60}")
        print("SUMMARY STATISTICS")
        print(f"{'='*60}")
        print(f"Total comparisons: {len(df)}")

        # Filter out error cases for meaningful statistics
        valid_df = df[~df['has_error']]
        error_df = df[df['has_error']]

        if len(error_df) > 0:
            print(f"⚠️  Cases with errors: {len(error_df)} ({len(error_df)/len(df)*100:.1f}%)")
            print(f"   Valid comparisons: {len(valid_df)} ({len(valid_df)/len(df)*100:.1f}%)")

        if len(valid_df) > 0:
            print(f"\nValid Cases Analysis:")
            print(f"Path allocation better: {valid_df['is_path_better'].sum()} ({valid_df['is_path_better'].mean()*100:.1f}%)")
            print(f"Benchmark better: {(~valid_df['is_path_better']).sum()} ({(~valid_df['is_path_better']).mean()*100:.1f}%)")

            if valid_df['percentage_difference'].notna().any():
                print(f"\nPercentage Difference Statistics (Valid Cases Only):")
                print(f"  Mean: {valid_df['percentage_difference'].mean():.4f}%")
                print(f"  Median: {valid_df['percentage_difference'].median():.4f}%")
                print(f"  Std Dev: {valid_df['percentage_difference'].std():.4f}%")
                print(f"  Min: {valid_df['percentage_difference'].min():.4f}%")
                print(f"  Max: {valid_df['percentage_difference'].max():.4f}%")

            print(f"\nAbsolute Difference Statistics (Valid Cases Only):")
            print(f"  Mean: {valid_df['absolute_difference'].mean():,.0f}")
            print(f"  Median: {valid_df['absolute_difference'].median():,.0f}")
            print(f"  Total absolute improvement: {valid_df[valid_df['is_path_better']]['absolute_difference'].sum():,.0f}")
            print(f"  Total absolute loss: {valid_df[~valid_df['is_path_better']]['absolute_difference'].sum():,.0f}")

        if len(error_df) > 0:
            print(f"\n⚠️  Error Cases:")
            for _, row in error_df.iterrows():
                print(f"  {row['token_pair']} (amount: {row['input_amount']}): Path={row['path_to_amount']:,.0f}, Benchmark={row['benchmark_to_amount']:,.0f}")
        
    return df


def main():
    parser = argparse.ArgumentParser(description='Compare path allocation results with benchmark data')
    parser.add_argument('--path-allocation-dir', 
                       default='reranking/results/path_allocation_json',
                       help='Directory containing path allocation JSON files')
    parser.add_argument('--benchmark-dir', 
                       default='data/benchmark',
                       help='Directory containing benchmark JSON files')
    parser.add_argument('--output', 
                       default='path_allocation_vs_benchmark_comparison.csv',
                       help='Output CSV file for results')
    parser.add_argument('--block-number',
                       help='Filter by specific block number')
    
    args = parser.parse_args()
    
    # Check if directories exist
    if not os.path.exists(args.path_allocation_dir):
        print(f"Error: Path allocation directory not found: {args.path_allocation_dir}")
        return
    
    if not os.path.exists(args.benchmark_dir):
        print(f"Error: Benchmark directory not found: {args.benchmark_dir}")
        return
    
    print("Path Allocation vs Benchmark Comparison")
    print("="*50)
    print(f"Path allocation directory: {args.path_allocation_dir}")
    print(f"Benchmark directory: {args.benchmark_dir}")
    
    # Run comparison
    df = compare_allocations(args.path_allocation_dir, args.benchmark_dir, args.output)
    
    if len(df) > 0:
        print(f"\nTop 5 improvements (by percentage):")
        print(df[df['is_path_better']].head()[['token_pair', 'input_amount', 'percentage_difference']].to_string(index=False))
        
        print(f"\nTop 5 losses (by percentage):")
        print(df[~df['is_path_better']].head()[['token_pair', 'input_amount', 'percentage_difference']].to_string(index=False))
    else:
        print("No matching files found for comparison.")


if __name__ == "__main__":
    main()
