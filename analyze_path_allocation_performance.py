#!/usr/bin/env python3
"""
Advanced analysis script for path allocation vs benchmark performance.
Generates detailed reports and visualizations.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import argparse
from pathlib import Path
import json


def load_comparison_data(csv_file: str) -> pd.DataFrame:
    """Load the comparison data from CSV file."""
    try:
        df = pd.read_csv(csv_file)
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return None


def create_performance_visualizations(df: pd.DataFrame, output_dir: str = "analysis_plots"):
    """Create various visualizations for the performance analysis."""
    
    # Create output directory
    Path(output_dir).mkdir(exist_ok=True)
    
    # Filter out error cases for meaningful visualizations
    valid_df = df[~df['has_error']].copy()
    
    if len(valid_df) == 0:
        print("No valid data for visualization")
        return
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. Percentage Difference Distribution
    plt.figure(figsize=(12, 6))
    plt.subplot(1, 2, 1)
    plt.hist(valid_df['percentage_difference'], bins=20, alpha=0.7, edgecolor='black')
    plt.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Break-even')
    plt.xlabel('Percentage Difference (%)')
    plt.ylabel('Frequency')
    plt.title('Distribution of Performance Differences')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. Box plot of percentage differences by token pair
    plt.subplot(1, 2, 2)
    token_pairs = valid_df['token_pair'].unique()
    if len(token_pairs) <= 10:  # Only show if not too many pairs
        sns.boxplot(data=valid_df, x='token_pair', y='percentage_difference')
        plt.xticks(rotation=45)
        plt.xlabel('Token Pair')
        plt.ylabel('Percentage Difference (%)')
        plt.title('Performance by Token Pair')
    else:
        plt.text(0.5, 0.5, f'Too many token pairs ({len(token_pairs)}) to display', 
                ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('Performance by Token Pair (Too many to display)')
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/performance_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Scatter plot: Input Amount vs Performance
    plt.figure(figsize=(12, 8))
    
    # Convert input amounts to numeric for plotting
    valid_df['input_amount_numeric'] = pd.to_numeric(valid_df['input_amount'])
    
    # Create scatter plot
    colors = ['green' if x else 'red' for x in valid_df['is_path_better']]
    plt.scatter(valid_df['input_amount_numeric'], valid_df['percentage_difference'], 
               c=colors, alpha=0.6, s=50)
    
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5, label='Break-even')
    plt.xlabel('Input Amount')
    plt.ylabel('Percentage Difference (%)')
    plt.title('Performance vs Input Amount')
    plt.xscale('log')  # Log scale for better visualization of wide range
    plt.grid(True, alpha=0.3)
    
    # Add legend
    from matplotlib.patches import Patch
    legend_elements = [Patch(facecolor='green', label='Path Allocation Better'),
                      Patch(facecolor='red', label='Benchmark Better')]
    plt.legend(handles=legend_elements)
    
    plt.savefig(f"{output_dir}/performance_vs_amount.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. Absolute differences
    plt.figure(figsize=(10, 6))
    
    # Separate improvements and losses
    improvements = valid_df[valid_df['is_path_better']]['absolute_difference']
    losses = valid_df[~valid_df['is_path_better']]['absolute_difference']
    
    plt.subplot(1, 2, 1)
    if len(improvements) > 0:
        plt.hist(improvements, bins=10, alpha=0.7, color='green', edgecolor='black')
        plt.xlabel('Absolute Improvement')
        plt.ylabel('Frequency')
        plt.title('Distribution of Improvements')
        plt.ticklabel_format(style='scientific', axis='x', scilimits=(0,0))
    
    plt.subplot(1, 2, 2)
    if len(losses) > 0:
        plt.hist(losses, bins=10, alpha=0.7, color='red', edgecolor='black')
        plt.xlabel('Absolute Loss')
        plt.ylabel('Frequency')
        plt.title('Distribution of Losses')
        plt.ticklabel_format(style='scientific', axis='x', scilimits=(0,0))
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/absolute_differences.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Visualizations saved to {output_dir}/")


def generate_detailed_report(df: pd.DataFrame, output_file: str = "detailed_analysis_report.txt"):
    """Generate a detailed text report of the analysis."""
    
    with open(output_file, 'w') as f:
        f.write("="*80 + "\n")
        f.write("DETAILED PATH ALLOCATION vs BENCHMARK ANALYSIS REPORT\n")
        f.write("="*80 + "\n\n")
        
        # Overall statistics
        f.write("OVERALL STATISTICS\n")
        f.write("-"*40 + "\n")
        f.write(f"Total comparisons: {len(df)}\n")
        
        valid_df = df[~df['has_error']]
        error_df = df[df['has_error']]
        
        f.write(f"Valid comparisons: {len(valid_df)} ({len(valid_df)/len(df)*100:.1f}%)\n")
        f.write(f"Error cases: {len(error_df)} ({len(error_df)/len(df)*100:.1f}%)\n\n")
        
        if len(valid_df) > 0:
            # Performance breakdown
            better_count = valid_df['is_path_better'].sum()
            worse_count = len(valid_df) - better_count
            
            f.write("PERFORMANCE BREAKDOWN (Valid Cases Only)\n")
            f.write("-"*40 + "\n")
            f.write(f"Path allocation better: {better_count} ({better_count/len(valid_df)*100:.1f}%)\n")
            f.write(f"Benchmark better: {worse_count} ({worse_count/len(valid_df)*100:.1f}%)\n\n")
            
            # Statistical summary
            f.write("STATISTICAL SUMMARY\n")
            f.write("-"*40 + "\n")
            f.write(f"Percentage Difference Statistics:\n")
            f.write(f"  Mean: {valid_df['percentage_difference'].mean():.6f}%\n")
            f.write(f"  Median: {valid_df['percentage_difference'].median():.6f}%\n")
            f.write(f"  Std Dev: {valid_df['percentage_difference'].std():.6f}%\n")
            f.write(f"  Min: {valid_df['percentage_difference'].min():.6f}%\n")
            f.write(f"  Max: {valid_df['percentage_difference'].max():.6f}%\n\n")
            
            # Top performers
            f.write("TOP PERFORMING CASES\n")
            f.write("-"*40 + "\n")
            
            if len(valid_df[valid_df['is_path_better']]) > 0:
                f.write("Best Improvements (by percentage):\n")
                top_improvements = valid_df[valid_df['is_path_better']].nlargest(5, 'percentage_difference')
                for _, row in top_improvements.iterrows():
                    f.write(f"  {row['token_pair']} (amount: {row['input_amount']}): {row['percentage_difference']:.6f}%\n")
                f.write("\n")
            
            if len(valid_df[~valid_df['is_path_better']]) > 0:
                f.write("Worst Performances (by percentage):\n")
                worst_performances = valid_df[~valid_df['is_path_better']].nsmallest(5, 'percentage_difference')
                for _, row in worst_performances.iterrows():
                    f.write(f"  {row['token_pair']} (amount: {row['input_amount']}): {row['percentage_difference']:.6f}%\n")
                f.write("\n")
            
            # Token pair analysis
            f.write("TOKEN PAIR ANALYSIS\n")
            f.write("-"*40 + "\n")
            pair_stats = valid_df.groupby('token_pair').agg({
                'percentage_difference': ['count', 'mean', 'std'],
                'is_path_better': 'sum'
            }).round(6)
            
            for pair in pair_stats.index:
                count = pair_stats.loc[pair, ('percentage_difference', 'count')]
                mean_perf = pair_stats.loc[pair, ('percentage_difference', 'mean')]
                std_perf = pair_stats.loc[pair, ('percentage_difference', 'std')]
                better_count = pair_stats.loc[pair, ('is_path_better', 'sum')]
                
                f.write(f"{pair}:\n")
                f.write(f"  Cases: {count}\n")
                f.write(f"  Mean performance: {mean_perf:.6f}%\n")
                f.write(f"  Std deviation: {std_perf:.6f}%\n")
                f.write(f"  Path allocation better: {better_count}/{count} ({better_count/count*100:.1f}%)\n\n")
        
        # Error cases
        if len(error_df) > 0:
            f.write("ERROR CASES\n")
            f.write("-"*40 + "\n")
            for _, row in error_df.iterrows():
                f.write(f"{row['token_pair']} (amount: {row['input_amount']}):\n")
                f.write(f"  Path allocation toAmount: {row['path_to_amount']:,.0f}\n")
                f.write(f"  Benchmark toAmount: {row['benchmark_to_amount']:,.0f}\n")
                f.write(f"  Issue: {'Negative path allocation result' if row['path_to_amount'] < 0 else 'Other error'}\n\n")
    
    print(f"Detailed report saved to {output_file}")


def main():
    parser = argparse.ArgumentParser(description='Analyze path allocation performance vs benchmark')
    parser.add_argument('--input-csv', 
                       default='path_allocation_vs_benchmark_comparison_v2.csv',
                       help='Input CSV file with comparison data')
    parser.add_argument('--output-dir', 
                       default='analysis_output',
                       help='Output directory for analysis results')
    parser.add_argument('--no-plots', 
                       action='store_true',
                       help='Skip generating plots')
    
    args = parser.parse_args()
    
    # Load data
    df = load_comparison_data(args.input_csv)
    if df is None:
        return
    
    print(f"Loaded {len(df)} comparison records")
    
    # Create output directory
    Path(args.output_dir).mkdir(exist_ok=True)
    
    # Generate detailed report
    report_file = f"{args.output_dir}/detailed_analysis_report.txt"
    generate_detailed_report(df, report_file)
    
    # Generate visualizations
    if not args.no_plots:
        plot_dir = f"{args.output_dir}/plots"
        create_performance_visualizations(df, plot_dir)
    
    # Save processed data with additional metrics
    enhanced_csv = f"{args.output_dir}/enhanced_comparison_data.csv"
    df.to_csv(enhanced_csv, index=False)
    print(f"Enhanced data saved to {enhanced_csv}")
    
    print(f"\nAnalysis complete! Results saved to {args.output_dir}/")


if __name__ == "__main__":
    main()
